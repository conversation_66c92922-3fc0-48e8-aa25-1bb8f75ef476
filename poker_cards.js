// WebSocket客户端连接和消息处理
class PokerWebSocketClient {
    constructor(url, userAccount) {
        this.url = url;
        this.userAccount = userAccount;
        this.ws = null;
        this.isConnected = false;
    }

    // 连接WebSocket服务器
    connect() {
        try {
            this.ws = new WebSocket(this.url);
            
            this.ws.onopen = (event) => {
                console.log('扑克牌WebSocket连接成功');
                this.isConnected = true;
                this.updateConnectionStatus('已连接');
                
                // 连接成功后立即发送账号数据
                this.sendAccountData();
            };

            this.ws.onmessage = (event) => {
                try {
                    const response = JSON.parse(event.data);
                    console.log('收到服务器消息:', response);
                    
                    // 处理不同类型的消息
                    this.handleMessage(response);
                } catch (error) {
                    console.error('解析消息失败:', error);
                }
            };

            this.ws.onclose = (event) => {
                console.log('扑克牌WebSocket连接关闭');
                this.isConnected = false;
                this.updateConnectionStatus('连接断开');
                
                // 5秒后尝试重连
                setTimeout(() => {
                    console.log('尝试重新连接...');
                    this.connect();
                }, 5000);
            };

            this.ws.onerror = (error) => {
                console.error('扑克牌WebSocket连接错误:', error);
                this.updateConnectionStatus('连接错误');
            };

        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
        }
    }

    // 发送账号数据（web类型注册消息）
    sendAccountData() {
        if (!this.isConnected || !this.ws) {
            console.error('WebSocket未连接，无法发送数据');
            return;
        }

        const accountMessage = {
            type: "web",
            uid_from: this.userAccount,
            user_type: 1,
            code: 0,
            sign: "",
            msg: ""
        };

        try {
            this.ws.send(JSON.stringify(accountMessage));
            console.log('扑克牌账号数据已发送:', accountMessage);
        } catch (error) {
            console.error('发送账号数据失败:', error);
        }
    }

    // 处理接收到的消息
    handleMessage(response) {
        switch (response.msg_type) {
            case 'sys':
                console.log('系统消息:', response.msg);
                if (response.code === 999) {
                    console.log('扑克牌账号注册成功');
                    this.updateConnectionStatus('注册成功');
                }
                break;
                
            case 'message':
                console.log('收到新的扑克牌数据:', response.data);
                this.updatePokerCards(response.data);
                break;
                
            default:
                console.log('未知消息类型:', response);
        }
    }

    // 更新扑克牌数据
    updatePokerCards(messageData) {
        try {
            console.log('=== 开始更新扑克牌数据 ===');
            console.log('接收到的数据类型:', typeof messageData);
            console.log('接收到的数据:', messageData);
            console.log('数据长度:', messageData ? messageData.length : 'null');
            console.log('条件检查:');
            console.log('- messageData存在:', !!messageData);
            console.log('- messageData是对象:', typeof messageData === 'object');
            console.log('- 有content字段:', messageData && messageData.content ? '是' : '否');
            console.log('- content是字符串:', messageData && typeof messageData.content === 'string' ? '是' : '否');
            
            let newCardData;
            
            // 检查是否是1.txt格式：{type: 0, content: "..."}
            if (messageData && typeof messageData === 'object' && messageData.content && typeof messageData.content === 'string') {
                console.log('检测到1.txt格式，解析content内容...');
                try {
                    newCardData = JSON.parse(messageData.content);
                    console.log('content解析成功，数据类型:', typeof newCardData);
                    console.log('解析后的数据键名:', Object.keys(newCardData || {}));
                } catch (e) {
                    console.error('解析content字段失败:', e);
                    this.showUpdateNotification('解析content字段失败: ' + e.message);
                    return;
                }
            } else if (typeof messageData === 'string') {
                console.log('数据是字符串，尝试解析JSON...');
                try {
                    newCardData = JSON.parse(messageData);
                    console.log('JSON解析成功:', newCardData);
                } catch (e) {
                    console.error('解析JSON失败，尝试直接使用数据:', e);
                    console.log('原始数据内容:', messageData);
                    
                    // 即使解析失败，也显示原始数据
                    this.showUpdateNotification('收到原始数据: ' + messageData.substring(0, 100) + (messageData.length > 100 ? '...' : ''));
                    return;
                }
            } else {
                console.log('数据不是字符串，直接使用');
                newCardData = messageData;
            }
            
            console.log('解析后的数据:', newCardData);
            console.log('数据类型检查:');
            console.log('- newCardData是否存在:', !!newCardData);
            console.log('- newCardData类型:', typeof newCardData);
            console.log('- 是否有useCardArray:', newCardData && newCardData.useCardArray ? '是' : '否');
            console.log('- useCardArray长度:', newCardData && newCardData.useCardArray ? newCardData.useCardArray.length : 'N/A');
            console.log('- 是否有lunSectionArray:', newCardData && newCardData.lunSectionArray ? '是' : '否');
            console.log('- 是否是数组:', Array.isArray(newCardData) ? '是' : '否');
            console.log('- 是否是字符串:', typeof newCardData === 'string' ? '是' : '否');
            
            // 输出newCardData的实际结构
            console.log('newCardData的键名:', Object.keys(newCardData || {}));
            console.log('newCardData的完整结构:', JSON.stringify(newCardData, null, 2).substring(0, 500) + '...');
            
            // 检查数据格式
            if (newCardData && newCardData.useCardArray) {
                console.log('✅ 检测到完整的牌组数据，更新所有牌...');
                console.log('牌数组长度:', newCardData.useCardArray.length);
                console.log('轮次数组长度:', newCardData.lunSectionArray ? newCardData.lunSectionArray.length : 0);
                
                // 更新全局数据
                cardData = newCardData;
                allCards = newCardData.useCardArray;
                
                // 重新显示所有内容
                displayCards();
                displaySections();
                updateStats();
                
                this.showUpdateNotification('牌组数据已更新 - ' + allCards.length + '张牌');
                
            } else if (newCardData && newCardData.content && typeof newCardData.content === 'string') {
                console.log('✅ 检测到content字段，解析content内容...');
                try {
                    const contentData = JSON.parse(newCardData.content);
                    console.log('content解析成功，检查是否有useCardArray...');
                    
                    if (contentData && contentData.useCardArray) {
                        console.log('✅ content中包含完整的牌组数据，更新所有牌...');
                        console.log('牌数组长度:', contentData.useCardArray.length);
                        console.log('轮次数组长度:', contentData.lunSectionArray ? contentData.lunSectionArray.length : 0);
                        
                        // 更新全局数据
                        cardData = contentData;
                        allCards = contentData.useCardArray;
                        
                        // 重新显示所有内容
                        displayCards();
                        displaySections();
                        updateStats();
                        
                        this.showUpdateNotification('牌组数据已更新 - ' + allCards.length + '张牌');
                    } else {
                        console.log('content中没有useCardArray字段');
                        this.showUpdateNotification('content中没有找到牌组数据');
                    }
                } catch (e) {
                    console.error('解析content内容失败:', e);
                    this.showUpdateNotification('解析content内容失败: ' + e.message);
                }
                
            } else if (Array.isArray(newCardData)) {
                console.log('✅ 检测到牌数组数据，更新牌列表...');
                
                // 直接更新牌数组
                allCards = newCardData;
                if (cardData) {
                    cardData.useCardArray = newCardData;
                }
                
                displayCards();
                updateStats();
                
                this.showUpdateNotification('牌数据已更新');
                
            } else {
                console.log('尝试解析为单个牌或特殊指令...');
                console.log('数据内容详情:', newCardData);
                
                // 检查是否是特殊指令
                if (typeof newCardData === 'string') {
                    const command = newCardData.toLowerCase().trim();
                    
                    switch (command) {
                        case 'shuffle':
                        case '洗牌':
                            console.log('执行洗牌指令');
                            shuffleCards();
                            this.showUpdateNotification('牌已洗牌');
                            break;
                            
                        case 'reset':
                        case '重置':
                            console.log('执行重置指令');
                            deselectAllCards();
                            this.showUpdateNotification('牌已重置');
                            break;
                            
                        case 'select_all':
                        case '全选':
                            console.log('执行全选指令');
                            selectAllCards();
                            this.showUpdateNotification('已全选所有牌');
                            break;
                            
                        default:
                            console.log('未识别的指令，显示为消息:', newCardData);
                            this.showUpdateNotification('收到消息: ' + newCardData);
                    }
                } else {
                    console.log('未知数据格式，显示原始数据');
                    this.showUpdateNotification('收到数据: ' + JSON.stringify(newCardData));
                }
            }
            
        } catch (error) {
            console.error('更新扑克牌数据失败:', error);
            this.showUpdateNotification('数据更新失败: ' + error.message);
        }
    }

    // 显示更新通知
    showUpdateNotification(message) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'update-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            font-size: 14px;
            max-width: 300px;
        `;
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 更新连接状态显示
    updateConnectionStatus(status) {
        // 尝试更新状态显示
        const statusElement = document.getElementById('poker-connection-status');
        if (statusElement) {
            statusElement.textContent = `扑克牌连接状态: ${status}`;
            statusElement.className = status.includes('已连接') || status.includes('注册成功') ? 'connected' : 'disconnected';
        } else {
            // 如果没有状态元素，创建一个
            const statusDiv = document.createElement('div');
            statusDiv.id = 'poker-connection-status';
            statusDiv.textContent = `扑克牌连接状态: ${status}`;
            statusDiv.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                padding: 5px 10px;
                background: ${status.includes('已连接') || status.includes('注册成功') ? '#4CAF50' : '#f44336'};
                color: white;
                border-radius: 3px;
                font-size: 12px;
                z-index: 999;
            `;
            document.body.appendChild(statusDiv);
        }
        
        console.log('扑克牌连接状态:', status);
    }

    // 断开连接
    disconnect() {
        if (this.ws) {
            this.isConnected = false;
            this.ws.close();
        }
    }
}

// 全局WebSocket客户端实例
let pokerWsClient = null;

// 全局变量
let cardData = null;
let allCards = [];
let currentDisplayMode = 'all'; // 'all' 或 'selected'

// 花色映射
const suitMapping = {
    'A': { name: '黑桃', symbol: '♠', class: 'spades' },
    'B': { name: '红桃', symbol: '♥', class: 'hearts' },
    'C': { name: '梅花', symbol: '♣', class: 'clubs' },
    'D': { name: '方块', symbol: '♦', class: 'diamonds' }
};

// 牌值映射
const valueMapping = {
    '1': 'A',
    '11': 'J',
    '12': 'Q',
    '13': 'K'
};

// 页面加载时自动加载数据
document.addEventListener('DOMContentLoaded', function() {
    // 首先加载本地数据
    loadCards();
    
    // 初始化WebSocket连接
    initializeWebSocket();
});

// 初始化WebSocket连接
function initializeWebSocket() {
    try {
        // 使用固定的账号 C250724672
        const userAccount = 'C250727503';
        
        console.log('使用扑克牌账号:', userAccount);
        
        // 创建WebSocket客户端
        pokerWsClient = new PokerWebSocketClient('ws://localhost:5311', userAccount);
        
        // 连接服务器
        pokerWsClient.connect();
        
        // 将客户端实例保存到全局变量，方便调试
        window.pokerWsClient = pokerWsClient;
        
        console.log('扑克牌WebSocket客户端已初始化');
        
    } catch (error) {
        console.error('初始化WebSocket连接失败:', error);
    }
}

// 加载扑克牌数据（现在完全依赖WebSocket数据）
function loadCards() {
    console.log('等待WebSocket数据...');
    
    try {
        const container = document.getElementById('cardContainer');
        const sectionsContainer = document.getElementById('sectionsContainer');
        
        if (container) {
            container.innerHTML = `
                <div class="loading">
                    等待接收扑克牌数据...<br>
                    请确保移动端已连接并发送数据
                </div>
            `;
        }
        
        if (sectionsContainer) {
            sectionsContainer.innerHTML = '<h2>轮次分组</h2><p>等待接收轮次分组数据...</p>';
        }
        
        // 更新统计信息
        updateStats();
        
        console.log('界面已初始化，等待WebSocket数据');
        
    } catch (error) {
        console.error('初始化界面失败:', error);
        console.error('错误堆栈:', error.stack);
        
        const container = document.getElementById('cardContainer');
        if (container) {
            container.innerHTML = `
                <div class="loading">
                    界面初始化失败<br>
                    错误信息: ${error.message}<br>
                    <button onclick="loadCards()" style="margin-top: 10px; padding: 5px 10px;">重试</button>
                </div>
            `;
        }
    }
}

// 显示扑克牌
function displayCards() {
    const container = document.getElementById('cardContainer');
    container.innerHTML = '';
    
    const cardsToShow = currentDisplayMode === 'selected' 
        ? allCards.filter(card => card.isSelect) 
        : allCards;
    
    cardsToShow.forEach(card => {
        const cardElement = createCardElement(card);
        container.appendChild(cardElement);
    });
}

// 创建扑克牌元素
function createCardElement(card) {
    const cardDiv = document.createElement('div');
    // 处理特殊牌（大王、小王）
    if (card.name === 'WA' || card.name === 'WB') {
        // 不使用joker类，避免CSS冲突
        cardDiv.className = `playing-card ${card.isSelect ? 'selected' : ''} custom-joker`;
        
        // 调试信息
        console.log('处理大小王:', card.name, 'cardName:', card.cardName);
        
        // 确保显示正确的文字
        let jokerText;
        if (card.name === 'WA') {
            jokerText = '小王';
        } else if (card.name === 'WB') {
            jokerText = '大王';
        }
        
        // 如果cardName为空或者不是预期的文字，使用我们的默认值
        if (!card.cardName || (card.cardName !== '小王' && card.cardName !== '大王')) {
            console.log('使用默认大小王文字:', jokerText);
        } else {
            jokerText = card.cardName;
            console.log('使用数据中的cardName:', jokerText);
        }
        
        // 使用与CSS匹配的HTML结构
        cardDiv.innerHTML = jokerText;
        
        // 完全自定义样式，不依赖CSS类
        cardDiv.style.cssText = `
            width: 70px !important;
            height: 98px !important;
            background: linear-gradient(45deg, #f39c12, #e67e22) !important;
            border: 1px solid #d35400 !important;
            border-radius: 6px !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            font-size: 16px !important;
            font-weight: bold !important;
            color: white !important;
            text-align: center !important;
            visibility: visible !important;
            opacity: 1 !important;
            line-height: 1 !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            font-family: Arial, sans-serif !important;
        `;
        
        return cardDiv;
    }
    
    // 普通牌处理
    cardDiv.className = `playing-card ${card.isSelect ? 'selected' : ''}`;
    cardDiv.setAttribute('data-name', card.name);
    
    // 解析牌名
    const { value, suit, suitInfo } = parseCardName(card.name);
    
    if (!suitInfo) {
        console.warn('未知花色:', card.name);
        // 创建一个默认卡片
        cardDiv.innerHTML = `
            <div class="card-center">
                ${card.name}
            </div>
        `;
        return cardDiv;
    }
    
    // 创建牌面内容 - 真实扑克牌布局
    cardDiv.classList.add(suitInfo.class);
    
    // 根据牌值决定中间显示的内容
    let centerContent = '';
    if (value === 'J' || value === 'Q' || value === 'K') {
        // 人头牌显示字母
        centerContent = `<div class="face-card">${value}</div>`;
    } else if (value === 'A') {
        // A显示大的花色符号
        centerContent = `<div class="ace-symbol">${suitInfo.symbol}</div>`;
    } else {
        // 数字牌显示对应数量的花色符号
        const num = parseInt(value) || 1;
        centerContent = generatePipLayout(suitInfo.symbol, num);
    }
    
    cardDiv.innerHTML = `
        <div class="card-corner top-left">
            <div class="corner-value">${value}</div>
            <div class="corner-suit">${suitInfo.symbol}</div>
        </div>
        
        <div class="card-center-area">
            ${centerContent}
        </div>
        
        <div class="card-corner bottom-right">
            <div class="corner-value">${value}</div>
            <div class="corner-suit">${suitInfo.symbol}</div>
        </div>
    `;
    
    // 添加点击事件
    cardDiv.addEventListener('click', function() {
        toggleCardSelection(card);
    });
    
    return cardDiv;
}

// 生成数字牌的点数布局
function generatePipLayout(symbol, count) {
    const layouts = {
        1: `<div class="pip-container">
                <div class="pip center">${symbol}</div>
            </div>`,
        
        2: `<div class="pip-container">
                <div class="pip top-center">${symbol}</div>
                <div class="pip bottom-center">${symbol}</div>
            </div>`,
        
        3: `<div class="pip-container">
                <div class="pip top-center">${symbol}</div>
                <div class="pip center">${symbol}</div>
                <div class="pip bottom-center">${symbol}</div>
            </div>`,
        
        4: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        5: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip center">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        6: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip mid-left">${symbol}</div>
                <div class="pip mid-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        7: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip mid-left">${symbol}</div>
                <div class="pip center">${symbol}</div>
                <div class="pip mid-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        8: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip mid-left">${symbol}</div>
                <div class="pip mid-right">${symbol}</div>
                <div class="pip mid-center-top">${symbol}</div>
                <div class="pip mid-center-bottom">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        9: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip upper-left">${symbol}</div>
                <div class="pip upper-right">${symbol}</div>
                <div class="pip center">${symbol}</div>
                <div class="pip lower-left">${symbol}</div>
                <div class="pip lower-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        10: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip upper-left">${symbol}</div>
                <div class="pip upper-right">${symbol}</div>
                <div class="pip mid-center-top">${symbol}</div>
                <div class="pip mid-center-bottom">${symbol}</div>
                <div class="pip lower-left">${symbol}</div>
                <div class="pip lower-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`
    };
    
    return layouts[count] || `<div class="pip-container"><div class="pip center">${symbol}</div></div>`;
}

// 解析牌名
function parseCardName(name) {
    if (name.length < 2) return { value: name, suit: '', suitInfo: null };
    
    const suit = name.slice(-1); // 最后一个字符是花色
    const value = name.slice(0, -1); // 前面的是牌值
    const suitInfo = suitMapping[suit];
    
    // 转换特殊牌值
    const displayValue = valueMapping[value] || value;
    
    return { value: displayValue, suit, suitInfo };
}

// 切换牌的选中状态
function toggleCardSelection(card) {
    card.isSelect = !card.isSelect;
    displayCards();
    updateStats();
}

// 显示轮次分组
function displaySections() {
    const container = document.getElementById('sectionsContainer');
    
    console.log('开始显示轮次分组...');
    console.log('cardData:', cardData);
    console.log('lunSectionArray:', cardData.lunSectionArray);
    console.log('lunSectionArray长度:', cardData.lunSectionArray ? cardData.lunSectionArray.length : 0);
    
    if (!cardData.lunSectionArray || cardData.lunSectionArray.length === 0) {
        container.innerHTML = '<h2>轮次分组</h2><p>没有轮次分组数据</p>';
        return;
    }
    
    let html = `<h2>轮次分组 (共${cardData.lunSectionArray.length}组)</h2>`;
    
    try {
        cardData.lunSectionArray.forEach((section, sectionIndex) => {
            console.log(`处理第${sectionIndex + 1}个section (index: ${section.index}):`, section);
            
            if (!section || !section.lunArray) {
                console.warn('无效的section数据:', section);
                return;
            }
            
            html += `
                <div class="section-simple">
                    <div class="section-title">第 ${section.index} 轮</div>
                    <div class="cut-card-simple">
                        <span class="cut-card-label">切牌:</span>
                        ${createSmallCardElement(allCards[allCards.length - 1])}
                    </div>
                    ${section.lunArray.map((pair, pairIndex) => {
                        console.log(`处理第${pairIndex + 1}个pair:`, pair);
                        
                        if (!Array.isArray(pair)) {
                            console.warn('pair不是数组:', pair);
                            return '<div class="card-pair-simple"><p>对子数据格式错误</p></div>';
                        }
                        
                        return `
                            <div class="card-pair-simple">
                                <div class="pair-cards">
                                    ${pair.map(cardRef => {
                                        console.log('处理cardRef:', cardRef);
                                        
                                        if (!cardRef || !cardRef.name) {
                                            console.warn('无效的cardRef:', cardRef);
                                            return '<span>无效卡片</span>';
                                        }
                                        
                                        // 使用当前的allCards数组中的牌，确保状态同步
                                        const card = allCards.find(c => c.name === cardRef.name);
                                        if (!card) {
                                            console.warn('找不到卡片:', cardRef.name);
                                            return '<span>找不到: ' + cardRef.name + '</span>';
                                        }
                                        
                                        return createSmallCardElement(card);
                                    }).join('')}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        });
        
        container.innerHTML = html;
        console.log('轮次分组显示完成');
        
    } catch (error) {
        console.error('显示轮次分组时出错:', error);
        console.error('错误堆栈:', error.stack);
        container.innerHTML = '<h2>轮次分组</h2><p>显示轮次分组时出错: ' + error.message + '</p>';
    }
}

// 根据名称查找牌
function findCardByName(name) {
    return allCards.find(card => card.name === name);
}

// 创建小尺寸扑克牌元素
function createSmallCardElement(card) {
    if (card.name === 'WA' || card.name === 'WB') {
        const jokerText = card.name === 'WA' ? '小王' : '大王';
        return `
            <div style="
                width: 80px !important; 
                height: 112px !important; 
                background: linear-gradient(45deg, #f39c12, #e67e22) !important;
                border: 1px solid #d35400 !important;
                border-radius: 6px !important;
                display: flex !important; 
                justify-content: center !important; 
                align-items: center !important; 
                color: white !important; 
                font-size: 16px !important; 
                font-weight: bold !important;
                text-align: center !important;
                visibility: visible !important;
                opacity: 1 !important;
                line-height: 1 !important;
                cursor: pointer !important;
                transition: all 0.2s ease !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
                font-family: Arial, sans-serif !important;
                ${card.isSelect ? 'border-color: #3498db !important; box-shadow: 0 0 10px rgba(52, 152, 219, 0.3) !important;' : ''}
            ">
                ${jokerText}
            </div>
        `;
    }
    
    const { value, suit, suitInfo } = parseCardName(card.name);
    
    if (!suitInfo) {
        console.warn('无法解析卡片:', card);
        return `<span style="color: red;">错误: ${card.name}</span>`;
    }
    
    // 更大的卡片布局
    return `
        <div class="playing-card ${suitInfo.class} ${card.isSelect ? 'selected' : ''}" style="width: 80px; height: 112px; padding: 6px; font-size: 12px;">
            <div class="card-corner top-left" style="font-size: 12px; top: 4px; left: 4px;">
                <div class="corner-value" style="font-size: 14px;">${value}</div>
                <div class="corner-suit" style="font-size: 11px;">${suitInfo.symbol}</div>
            </div>
            
            <div class="card-center-area" style="top: 20px; bottom: 20px; left: 8px; right: 8px;">
                ${value === 'J' || value === 'Q' || value === 'K' ? 
                    `<div class="face-card" style="font-size: 20px;">${value}</div>` :
                    value === 'A' ? 
                    `<div class="ace-symbol" style="font-size: 22px;">${suitInfo.symbol}</div>` :
                    generateSmallPipLayout(suitInfo.symbol, parseInt(value) || 1)
                }
            </div>
            
            <div class="card-corner bottom-right" style="font-size: 12px; bottom: 4px; right: 4px; transform: rotate(180deg);">
                <div class="corner-value" style="font-size: 14px;">${value}</div>
                <div class="corner-suit" style="font-size: 11px;">${suitInfo.symbol}</div>
            </div>
        </div>
    `;
}

// 生成小尺寸数字牌的点数布局
function generateSmallPipLayout(symbol, count) {
    const layouts = {
        1: `<div class="pip-container">
                <div class="pip center">${symbol}</div>
            </div>`,
        
        2: `<div class="pip-container">
                <div class="pip top-center">${symbol}</div>
                <div class="pip bottom-center">${symbol}</div>
            </div>`,
        
        3: `<div class="pip-container">
                <div class="pip top-center">${symbol}</div>
                <div class="pip center">${symbol}</div>
                <div class="pip bottom-center">${symbol}</div>
            </div>`,
        
        4: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        5: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip center">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        6: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip mid-left">${symbol}</div>
                <div class="pip mid-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        7: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip mid-left">${symbol}</div>
                <div class="pip center">${symbol}</div>
                <div class="pip mid-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        8: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip mid-left">${symbol}</div>
                <div class="pip mid-right">${symbol}</div>
                <div class="pip mid-center-top">${symbol}</div>
                <div class="pip mid-center-bottom">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        9: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip upper-left">${symbol}</div>
                <div class="pip upper-right">${symbol}</div>
                <div class="pip center">${symbol}</div>
                <div class="pip lower-left">${symbol}</div>
                <div class="pip lower-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`,
        
        10: `<div class="pip-container">
                <div class="pip top-left">${symbol}</div>
                <div class="pip top-right">${symbol}</div>
                <div class="pip upper-left">${symbol}</div>
                <div class="pip upper-right">${symbol}</div>
                <div class="pip mid-center-top">${symbol}</div>
                <div class="pip mid-center-bottom">${symbol}</div>
                <div class="pip lower-left">${symbol}</div>
                <div class="pip lower-right">${symbol}</div>
                <div class="pip bottom-left">${symbol}</div>
                <div class="pip bottom-right">${symbol}</div>
            </div>`
    };
    
    return layouts[count] || `<div class="pip-container"><div class="pip center">${symbol}</div></div>`;
}

// 仅显示选中的牌
function showSelectedOnly() {
    currentDisplayMode = 'selected';
    displayCards();
    updateStats();
}

// 显示所有牌
function showAllCards() {
    currentDisplayMode = 'all';
    displayCards();
    updateStats();
}

// 更新统计信息
function updateStats() {
    const totalCards = allCards ? allCards.length : 0;
    const roundCount = cardData && cardData.lunSectionArray ? cardData.lunSectionArray.length : 0;
    
    const cardCountElement = document.getElementById('cardCount');
    const roundCountElement = document.getElementById('roundCount');
    
    if (cardCountElement) {
        cardCountElement.textContent = `牌数: ${totalCards}`;
    }
    
    if (roundCountElement) {
        roundCountElement.textContent = `轮组: ${roundCount}`;
    }
}

// 添加一些实用功能
function selectAllCards() {
    allCards.forEach(card => card.isSelect = true);
    displayCards();
    updateStats();
}

function deselectAllCards() {
    allCards.forEach(card => card.isSelect = false);
    displayCards();
    updateStats();
}

function shuffleCards() {
    for (let i = allCards.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [allCards[i], allCards[j]] = [allCards[j], allCards[i]];
    }
    displayCards();
}

// 导出数据功能
function exportSelectedCards() {
    const selectedCards = allCards.filter(card => card.isSelect);
    const dataStr = JSON.stringify(selectedCards, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'selected_cards.json';
    link.click();
}

// 添加键盘快捷键
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 'a':
                e.preventDefault();
                selectAllCards();
                break;
            case 'd':
                e.preventDefault();
                deselectAllCards();
                break;
            case 's':
                e.preventDefault();
                exportSelectedCards();
                break;
        }
    }
});

// 添加右键菜单功能
document.addEventListener('contextmenu', function(e) {
    if (e.target.classList.contains('playing-card')) {
        e.preventDefault();
        // 这里可以添加右键菜单功能
    }
});

console.log('扑克牌显示器已初始化');
console.log('快捷键: Ctrl+A(全选), Ctrl+D(取消全选), Ctrl+S(导出选中的牌)'); 