* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    padding: 0;
}

.container {
    width: 100vw;
    min-height: 100vh;
    margin: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 0;
    padding: 20px;
    box-shadow: none;
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
    color: #34495e;
    margin: 30px 0 20px 0;
    font-size: 1.8em;
}

.controls {
    text-align: center;
    margin-bottom: 20px;
}

.controls button {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 12px 24px;
    margin: 0 10px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.stats {
    text-align: center;
    margin-bottom: 30px;
    font-size: 18px;
    color: #34495e;
}

.stats span {
    margin: 0 20px;
    font-weight: bold;
}

.card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
    margin-bottom: 40px;
    max-width: 100%;
}

.playing-card {
    width: 70px;
    height: 98px;
    background: white;
    border-radius: 6px;
    border: 1px solid #666;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 6px;
    font-family: 'Times New Roman', serif;
    font-weight: bold;
}

.playing-card:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.playing-card.selected {
    border-color: #3498db;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
    background: linear-gradient(145deg, #fff, #f8f9fa);
}

/* 扑克牌角落标记 */
.card-corner {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
}

.card-corner.top-left {
    top: 3px;
    left: 3px;
}

.card-corner.bottom-right {
    bottom: 3px;
    right: 3px;
    transform: rotate(180deg);
}

.corner-value {
    font-size: 16px;
    margin-bottom: 2px;
    font-weight: 900;
}

.corner-suit {
    font-size: 12px;
    font-weight: bold;
}

/* 扑克牌中间区域 */
.card-center-area {
    position: absolute;
    top: 18px;
    left: 6px;
    right: 6px;
    bottom: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 点数容器 */
.pip-container {
    position: relative;
    width: 100%;
    height: 100%;
}

/* 点数布局 */
.pip {
    position: absolute;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 基础位置 - 中心点 */
.pip.center { 
    top: 50%; 
    left: 50%; 
    transform: translate(-50%, -50%); 
}

/* 上下中心 */
.pip.top-center { 
    top: 10%; 
    left: 50%; 
    transform: translate(-50%, -50%); 
}
.pip.bottom-center { 
    bottom: 10%; 
    left: 50%; 
    transform: translate(-50%, 50%) rotate(180deg); 
}

/* 四角位置 */
.pip.top-left { 
    top: 12%; 
    left: 25%; 
    transform: translate(-50%, -50%); 
}
.pip.top-right { 
    top: 12%; 
    right: 25%; 
    transform: translate(50%, -50%); 
}
.pip.bottom-left { 
    bottom: 12%; 
    left: 25%; 
    transform: translate(-50%, 50%) rotate(180deg); 
}
.pip.bottom-right { 
    bottom: 12%; 
    right: 25%; 
    transform: translate(50%, 50%) rotate(180deg); 
}

/* 上中下位置 */
.pip.upper-left { 
    top: 30%; 
    left: 25%; 
    transform: translate(-50%, -50%); 
}
.pip.upper-right { 
    top: 30%; 
    right: 25%; 
    transform: translate(50%, -50%); 
}
.pip.lower-left { 
    bottom: 30%; 
    left: 25%; 
    transform: translate(-50%, 50%) rotate(180deg); 
}
.pip.lower-right { 
    bottom: 30%; 
    right: 25%; 
    transform: translate(50%, 50%) rotate(180deg); 
}

/* 中间左右 */
.pip.mid-left { 
    top: 50%; 
    left: 25%; 
    transform: translate(-50%, -50%); 
}
.pip.mid-right { 
    top: 50%; 
    right: 25%; 
    transform: translate(50%, -50%); 
}

/* 8和10牌的中间位置 */
.pip.mid-center-top { 
    top: 40%; 
    left: 50%; 
    transform: translate(-50%, -50%); 
}
.pip.mid-center-bottom { 
    bottom: 40%; 
    left: 50%; 
    transform: translate(-50%, 50%) rotate(180deg); 
}

/* 9牌的额外位置 */
.pip.quarter-left { 
    top: 25%; 
    left: 30%; 
    transform: translate(-50%, -50%); 
}
.pip.quarter-right { 
    top: 75%; 
    right: 30%; 
    transform: translate(50%, -50%) rotate(180deg); 
}

/* 10牌的特殊位置 */
.pip.center-left { 
    top: 50%; 
    left: 50%; 
    transform: translate(-100%, -50%); 
}
.pip.center-right { 
    top: 50%; 
    left: 50%; 
    transform: translate(0%, -50%); 
}

/* 人头牌样式 */
.face-card {
    font-size: 28px;
    font-weight: 900;
    text-align: center;
    color: inherit;
}

/* A牌样式 */
.ace-symbol {
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    color: inherit;
}

/* 大王小王样式 */
.joker-content {
    font-size: 14px;
    font-weight: bold;
    text-align: center;
}

/* 移除旧的样式 */
.card-top, .card-bottom, .card-center {
    display: none;
}

/* 花色颜色 */
.hearts, .diamonds {
    color: #e74c3c;
}

.spades, .clubs {
    color: #2c3e50;
}

/* 特殊牌样式 */
.joker {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    text-align: center;
    justify-content: center;
    align-items: center;
    font-size: 11px;
    font-weight: bold;
    border: 1px solid #d35400;
}

.joker .card-center {
    position: static;
    transform: none;
    font-size: 16px;
    display: block;
}

/* 轮次分组样式 */
.sections-container {
    margin-top: 40px;
}

.section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(236, 240, 241, 0.8);
    border-radius: 10px;
    border-left: 5px solid #3498db;
}

.section-title {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
}

.lun-group {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #bdc3c7;
}

.lun-title {
    font-size: 16px;
    font-weight: bold;
    color: #34495e;
    margin-bottom: 10px;
}

.card-pair {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.pair-cards {
    display: flex;
    gap: 5px;
}

.index-info {
    margin-left: 15px;
    font-size: 14px;
    color: #7f8c8d;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }
    
    .playing-card {
        width: 60px;
        height: 84px;
        padding: 6px;
    }
    
    .card-value {
        font-size: 10px;
    }
    
    .card-suit {
        font-size: 12px;
    }
    
    .card-center {
        font-size: 18px;
    }
    
    .controls button {
        padding: 10px 16px;
        font-size: 14px;
        margin: 5px;
    }
}

/* 动画效果 */
@keyframes cardFlip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(90deg); }
    100% { transform: rotateY(0deg); }
}

.playing-card.flip {
    animation: cardFlip 0.6s ease-in-out;
}

/* 加载动画 */
.loading {
    text-align: center;
    font-size: 18px;
    color: #7f8c8d;
    margin: 40px 0;
}

.loading::after {
    content: "...";
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
    0%, 20% { content: "."; }
    40% { content: ".."; }
    60% { content: "..."; }
    80%, 100% { content: ""; }
} 

/* 简化的轮次分组样式 */
.section-simple {
    margin-bottom: 30px;
}

.section-simple .section-title {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-left: 8px;
    border-left: 3px solid #3498db;
}

.card-pair-simple {
    display: inline-block;
    margin: 10px 20px 10px 0;
    vertical-align: top;
}

.card-pair-simple .pair-cards {
    display: flex;
    gap: 10px;
    align-items: flex-start;
}

.card-pair-simple .index-info {
    font-size: 13px;
    color: #34495e;
    font-weight: bold;
    text-align: center;
    margin-top: 5px;
} 

/* 切牌区域样式 */
.cut-card-section {
    margin: 20px 0 10px 0;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 15px;
    background: rgba(52, 152, 219, 0.08);
    border-radius: 6px;
    border-left: 3px solid #3498db;
}

.cut-card-title {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
}

.cut-card {
    display: flex;
} 

/* 简化的切牌样式 */
.cut-card-simple {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 15px 0;
}

.cut-card-label {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
} 