<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>54张扑克牌</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            justify-items: center;
        }
        
        .card {
            width: 70px;
            height: 100px;
            background: white;
            border-radius: 8px;
            border: 2px solid #333;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 16px rgba(0,0,0,0.4);
        }
        
        .card.red {
            color: #d32f2f;
        }
        
        .card.black {
            color: #333;
        }
        
        .card-top {
            position: absolute;
            top: 3px;
            left: 3px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
        }

        .card-bottom {
            position: absolute;
            bottom: 3px;
            right: 3px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
            transform: rotate(180deg);
        }
        
        .card-center {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-grow: 1;
            font-size: 16px;
            flex-wrap: wrap;
            padding: 2px;
        }

        .card-center.single {
            font-size: 24px;
        }

        .card-center.multiple {
            font-size: 12px;
            line-height: 1;
        }
        
        .joker {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .joker.red-joker {
            background: linear-gradient(45deg, #ff4757, #ff3838);
        }
        
        .joker.black-joker {
            background: linear-gradient(45deg, #2f3542, #57606f);
        }
        
        .suit-display {
            margin-top: 20px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            text-align: center;
        }
        
        .suit-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(5px);
        }
        
        .suit-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        @media (max-width: 768px) {
            .cards-grid {
                grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
                gap: 8px;
            }
            
            .card {
                width: 55px;
                height: 80px;
                padding: 3px;
            }
            
            .card-top, .card-bottom {
                font-size: 10px;
            }
            
            .card-center {
                font-size: 16px;
            }
            
            .suit-display {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🃏 54张扑克牌 🃏</h1>
        <div class="cards-grid">

            <div class="card red" title="红桃A">
                <div class="card-top">
                    <div>A</div>
                    <div>♥</div>
                </div>
                <div class="card-center single">
                    <div class="single">A</div>
                </div>
                <div class="card-bottom">
                    <div>A</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃2">
                <div class="card-top">
                    <div>2</div>
                    <div>♥</div>
                </div>
                <div class="card-center multiple">
                    ♥<br>♥
                </div>
                <div class="card-bottom">
                    <div>2</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃3">
                <div class="card-top">
                    <div>3</div>
                    <div>♥</div>
                </div>
                <div class="card-center multiple">
                    ♥<br>♥<br>♥
                </div>
                <div class="card-bottom">
                    <div>3</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃4">
                <div class="card-top">
                    <div>4</div>
                    <div>♥</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥</div></div>
                </div>
                <div class="card-bottom">
                    <div>4</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃5">
                <div class="card-top">
                    <div>5</div>
                    <div>♥</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥</div></div>
                </div>
                <div class="card-bottom">
                    <div>5</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃6">
                <div class="card-top">
                    <div>6</div>
                    <div>♥</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥</div></div>
                </div>
                <div class="card-bottom">
                    <div>6</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃7">
                <div class="card-top">
                    <div>7</div>
                    <div>♥</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥</div></div>
                </div>
                <div class="card-bottom">
                    <div>7</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃8">
                <div class="card-top">
                    <div>8</div>
                    <div>♥</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥<br>♥</div></div>
                </div>
                <div class="card-bottom">
                    <div>8</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃9">
                <div class="card-top">
                    <div>9</div>
                    <div>♥</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥</div></div>
                </div>
                <div class="card-bottom">
                    <div>9</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃10">
                <div class="card-top">
                    <div>10</div>
                    <div>♥</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♥<br>♥<br>♥<br>♥</div></div>
                </div>
                <div class="card-bottom">
                    <div>10</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃J">
                <div class="card-top">
                    <div>J</div>
                    <div>♥</div>
                </div>
                <div class="card-center single">
                    <div class="single">J</div>
                </div>
                <div class="card-bottom">
                    <div>J</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃Q">
                <div class="card-top">
                    <div>Q</div>
                    <div>♥</div>
                </div>
                <div class="card-center single">
                    <div class="single">Q</div>
                </div>
                <div class="card-bottom">
                    <div>Q</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="红桃K">
                <div class="card-top">
                    <div>K</div>
                    <div>♥</div>
                </div>
                <div class="card-center single">
                    <div class="single">K</div>
                </div>
                <div class="card-bottom">
                    <div>K</div>
                    <div>♥</div>
                </div>
            </div>
            <div class="card red" title="方块A">
                <div class="card-top">
                    <div>A</div>
                    <div>♦</div>
                </div>
                <div class="card-center single">
                    <div class="single">A</div>
                </div>
                <div class="card-bottom">
                    <div>A</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块2">
                <div class="card-top">
                    <div>2</div>
                    <div>♦</div>
                </div>
                <div class="card-center multiple">
                    ♦<br>♦
                </div>
                <div class="card-bottom">
                    <div>2</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块3">
                <div class="card-top">
                    <div>3</div>
                    <div>♦</div>
                </div>
                <div class="card-center multiple">
                    ♦<br>♦<br>♦
                </div>
                <div class="card-bottom">
                    <div>3</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块4">
                <div class="card-top">
                    <div>4</div>
                    <div>♦</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦</div></div>
                </div>
                <div class="card-bottom">
                    <div>4</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块5">
                <div class="card-top">
                    <div>5</div>
                    <div>♦</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦</div></div>
                </div>
                <div class="card-bottom">
                    <div>5</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块6">
                <div class="card-top">
                    <div>6</div>
                    <div>♦</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦</div></div>
                </div>
                <div class="card-bottom">
                    <div>6</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块7">
                <div class="card-top">
                    <div>7</div>
                    <div>♦</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦</div></div>
                </div>
                <div class="card-bottom">
                    <div>7</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块8">
                <div class="card-top">
                    <div>8</div>
                    <div>♦</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦<br>♦</div></div>
                </div>
                <div class="card-bottom">
                    <div>8</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块9">
                <div class="card-top">
                    <div>9</div>
                    <div>♦</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦</div></div>
                </div>
                <div class="card-bottom">
                    <div>9</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块10">
                <div class="card-top">
                    <div>10</div>
                    <div>♦</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♦<br>♦<br>♦<br>♦</div></div>
                </div>
                <div class="card-bottom">
                    <div>10</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块J">
                <div class="card-top">
                    <div>J</div>
                    <div>♦</div>
                </div>
                <div class="card-center single">
                    <div class="single">J</div>
                </div>
                <div class="card-bottom">
                    <div>J</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块Q">
                <div class="card-top">
                    <div>Q</div>
                    <div>♦</div>
                </div>
                <div class="card-center single">
                    <div class="single">Q</div>
                </div>
                <div class="card-bottom">
                    <div>Q</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card red" title="方块K">
                <div class="card-top">
                    <div>K</div>
                    <div>♦</div>
                </div>
                <div class="card-center single">
                    <div class="single">K</div>
                </div>
                <div class="card-bottom">
                    <div>K</div>
                    <div>♦</div>
                </div>
            </div>
            <div class="card black" title="梅花A">
                <div class="card-top">
                    <div>A</div>
                    <div>♣</div>
                </div>
                <div class="card-center single">
                    <div class="single">A</div>
                </div>
                <div class="card-bottom">
                    <div>A</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花2">
                <div class="card-top">
                    <div>2</div>
                    <div>♣</div>
                </div>
                <div class="card-center multiple">
                    ♣<br>♣
                </div>
                <div class="card-bottom">
                    <div>2</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花3">
                <div class="card-top">
                    <div>3</div>
                    <div>♣</div>
                </div>
                <div class="card-center multiple">
                    ♣<br>♣<br>♣
                </div>
                <div class="card-bottom">
                    <div>3</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花4">
                <div class="card-top">
                    <div>4</div>
                    <div>♣</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣</div></div>
                </div>
                <div class="card-bottom">
                    <div>4</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花5">
                <div class="card-top">
                    <div>5</div>
                    <div>♣</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣</div></div>
                </div>
                <div class="card-bottom">
                    <div>5</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花6">
                <div class="card-top">
                    <div>6</div>
                    <div>♣</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣</div></div>
                </div>
                <div class="card-bottom">
                    <div>6</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花7">
                <div class="card-top">
                    <div>7</div>
                    <div>♣</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣</div></div>
                </div>
                <div class="card-bottom">
                    <div>7</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花8">
                <div class="card-top">
                    <div>8</div>
                    <div>♣</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣<br>♣</div></div>
                </div>
                <div class="card-bottom">
                    <div>8</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花9">
                <div class="card-top">
                    <div>9</div>
                    <div>♣</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣</div></div>
                </div>
                <div class="card-bottom">
                    <div>9</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花10">
                <div class="card-top">
                    <div>10</div>
                    <div>♣</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♣<br>♣<br>♣<br>♣</div></div>
                </div>
                <div class="card-bottom">
                    <div>10</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花J">
                <div class="card-top">
                    <div>J</div>
                    <div>♣</div>
                </div>
                <div class="card-center single">
                    <div class="single">J</div>
                </div>
                <div class="card-bottom">
                    <div>J</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花Q">
                <div class="card-top">
                    <div>Q</div>
                    <div>♣</div>
                </div>
                <div class="card-center single">
                    <div class="single">Q</div>
                </div>
                <div class="card-bottom">
                    <div>Q</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="梅花K">
                <div class="card-top">
                    <div>K</div>
                    <div>♣</div>
                </div>
                <div class="card-center single">
                    <div class="single">K</div>
                </div>
                <div class="card-bottom">
                    <div>K</div>
                    <div>♣</div>
                </div>
            </div>
            <div class="card black" title="黑桃A">
                <div class="card-top">
                    <div>A</div>
                    <div>♠</div>
                </div>
                <div class="card-center single">
                    <div class="single">A</div>
                </div>
                <div class="card-bottom">
                    <div>A</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃2">
                <div class="card-top">
                    <div>2</div>
                    <div>♠</div>
                </div>
                <div class="card-center multiple">
                    ♠<br>♠
                </div>
                <div class="card-bottom">
                    <div>2</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃3">
                <div class="card-top">
                    <div>3</div>
                    <div>♠</div>
                </div>
                <div class="card-center multiple">
                    ♠<br>♠<br>♠
                </div>
                <div class="card-bottom">
                    <div>3</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃4">
                <div class="card-top">
                    <div>4</div>
                    <div>♠</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠</div></div>
                </div>
                <div class="card-bottom">
                    <div>4</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃5">
                <div class="card-top">
                    <div>5</div>
                    <div>♠</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠</div></div>
                </div>
                <div class="card-bottom">
                    <div>5</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃6">
                <div class="card-top">
                    <div>6</div>
                    <div>♠</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠</div></div>
                </div>
                <div class="card-bottom">
                    <div>6</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃7">
                <div class="card-top">
                    <div>7</div>
                    <div>♠</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠</div></div>
                </div>
                <div class="card-bottom">
                    <div>7</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃8">
                <div class="card-top">
                    <div>8</div>
                    <div>♠</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠<br>♠</div></div>
                </div>
                <div class="card-bottom">
                    <div>8</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃9">
                <div class="card-top">
                    <div>9</div>
                    <div>♠</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠</div></div>
                </div>
                <div class="card-bottom">
                    <div>9</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃10">
                <div class="card-top">
                    <div>10</div>
                    <div>♠</div>
                </div>
                <div class="card-center multiple">
                    <div style="display: flex; justify-content: space-between; width: 100%; height: 100%;"><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠</div><div style="display: flex; flex-direction: column; justify-content: space-around;">♠<br>♠<br>♠<br>♠</div></div>
                </div>
                <div class="card-bottom">
                    <div>10</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃J">
                <div class="card-top">
                    <div>J</div>
                    <div>♠</div>
                </div>
                <div class="card-center single">
                    <div class="single">J</div>
                </div>
                <div class="card-bottom">
                    <div>J</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃Q">
                <div class="card-top">
                    <div>Q</div>
                    <div>♠</div>
                </div>
                <div class="card-center single">
                    <div class="single">Q</div>
                </div>
                <div class="card-bottom">
                    <div>Q</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card black" title="黑桃K">
                <div class="card-top">
                    <div>K</div>
                    <div>♠</div>
                </div>
                <div class="card-center single">
                    <div class="single">K</div>
                </div>
                <div class="card-bottom">
                    <div>K</div>
                    <div>♠</div>
                </div>
            </div>
            <div class="card joker red-joker" title="小王">
                <div class="card-top">
                    <div>小</div>
                    <div>王</div>
                </div>
                <div class="card-center">
                    🃏
                </div>
                <div class="card-bottom">
                    <div>小</div>
                    <div>王</div>
                </div>
            </div>
            
            <div class="card joker black-joker" title="大王">
                <div class="card-top">
                    <div>大</div>
                    <div>王</div>
                </div>
                <div class="card-center">
                    🃏
                </div>
                <div class="card-bottom">
                    <div>大</div>
                    <div>王</div>
                </div>
            </div>
        </div>
        
        <div class="suit-display">
            <div class="suit-section">
                <div class="suit-title">♠ 黑桃</div>
                <div style="color: white;">13张牌</div>
            </div>
            <div class="suit-section">
                <div class="suit-title">♥ 红桃</div>
                <div style="color: white;">13张牌</div>
            </div>
            <div class="suit-section">
                <div class="suit-title">♣ 梅花</div>
                <div style="color: white;">13张牌</div>
            </div>
            <div class="suit-section">
                <div class="suit-title">♦ 方块</div>
                <div style="color: white;">13张牌</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px; color: white;">
            <p>总计：52张普通牌 + 2张王牌 = 54张扑克牌</p>
            <p>鼠标悬停查看牌面详情</p>
        </div>
    </div>
    
    <script>
        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    // 点击效果
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                    
                    // 显示牌面信息
                    const title = this.getAttribute('title');
                    if (title) {
                        console.log('点击了：' + title);
                    }
                });
            });
            
            // 统计信息
            console.log('扑克牌生成完成！');
            console.log('总计：' + cards.length + '张牌');
        });
    </script>
</body>
</html>