// WebSocket客户端连接和消息处理
class WebSocketClient {
    constructor(url, userAccount) {
        this.url = url;
        this.userAccount = userAccount;
        this.ws = null;
        this.isConnected = false;
    }

    // 连接WebSocket服务器
    connect() {
        try {
            this.ws = new WebSocket(this.url);
            
            this.ws.onopen = (event) => {
                console.log('WebSocket连接成功');
                this.isConnected = true;
                
                // 连接成功后立即发送账号数据
                this.sendAccountData();
            };

            this.ws.onmessage = (event) => {
                try {
                    const response = JSON.parse(event.data);
                    console.log('收到服务器消息:', response);
                    
                    // 处理不同类型的消息
                    this.handleMessage(response);
                } catch (error) {
                    console.error('解析消息失败:', error);
                }
            };

            this.ws.onclose = (event) => {
                console.log('WebSocket连接关闭');
                this.isConnected = false;
                
                // 可以在这里实现重连逻辑
                setTimeout(() => {
                    console.log('尝试重新连接...');
                    this.connect();
                }, 5000);
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket连接错误:', error);
            };

        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
        }
    }

    // 发送账号数据（web类型注册消息）
    sendAccountData() {
        if (!this.isConnected || !this.ws) {
            console.error('WebSocket未连接，无法发送数据');
            return;
        }

        const accountMessage = {
            type: "web",
            uid_from: this.userAccount,
            user_type: 1,
            code: 0,
            sign: "",
            msg: ""
        };

        try {
            this.ws.send(JSON.stringify(accountMessage));
            console.log('账号数据已发送:', accountMessage);
        } catch (error) {
            console.error('发送账号数据失败:', error);
        }
    }

    // 处理接收到的消息
    handleMessage(response) {
        switch (response.msg_type) {
            case 'sys':
                console.log('系统消息:', response.msg);
                if (response.code === 999) {
                    console.log('账号注册成功');
                    this.onAccountRegistered(response);
                }
                break;
                
            case 'message':
                console.log('收到新消息:', response.data);
                this.onNewMessage(response.data);
                break;
                
            default:
                console.log('未知消息类型:', response);
        }
    }

    // 账号注册成功回调
    onAccountRegistered(response) {
        console.log('网页端账号注册成功，可以开始接收消息');
        // 这里可以更新UI状态，显示连接成功
        this.updateConnectionStatus('已连接');
    }

    // 收到新消息回调
    onNewMessage(messageData) {
        console.log('收到解码后的消息:', messageData);
        // 这里可以更新UI，显示收到的消息
        this.displayMessage(messageData);
    }

    // 更新连接状态显示
    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            statusElement.textContent = `连接状态: ${status}`;
            statusElement.className = status === '已连接' ? 'connected' : 'disconnected';
        }
    }

    // 显示收到的消息
    displayMessage(message) {
        const messagesContainer = document.getElementById('messages');
        if (messagesContainer) {
            const messageElement = document.createElement('div');
            messageElement.className = 'message';
            messageElement.innerHTML = `
                <div class="timestamp">${new Date().toLocaleTimeString()}</div>
                <div class="content">${message}</div>
            `;
            messagesContainer.appendChild(messageElement);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    // 断开连接
    disconnect() {
        if (this.ws) {
            this.isConnected = false;
            this.ws.close();
        }
    }
}

// 使用示例
document.addEventListener('DOMContentLoaded', function() {
    // 获取用户账号（可以从输入框、localStorage等获取）
    const userAccount = prompt('请输入您的账号标识:') || 'web_user_' + Date.now();
    
    // 创建WebSocket客户端
    const client = new WebSocketClient('ws://localhost:5311', userAccount);
    
    // 连接服务器
    client.connect();
    
    // 将客户端实例保存到全局变量，方便调试
    window.wsClient = client;
}); 